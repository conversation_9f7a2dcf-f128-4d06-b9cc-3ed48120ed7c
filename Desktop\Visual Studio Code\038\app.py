from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from datetime import datetime, timedelta
import uuid
import json

from config import Config
from models import db, User, CallLog, SipRegistration

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return render_template('index.html', user=current_user, config=app.config)
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        username = data.get('username')
        password = data.get('password')
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            if request.is_json:
                return jsonify({'success': True, 'redirect': url_for('index')})
            return redirect(url_for('index'))
        else:
            if request.is_json:
                return jsonify({'success': False, 'message': 'Invalid credentials'})
            flash('Invalid username or password')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    # Clean up SIP registrations
    SipRegistration.query.filter_by(user_id=current_user.id, is_active=True).update({'is_active': False})
    db.session.commit()
    logout_user()
    return redirect(url_for('login'))

@app.route('/admin')
@login_required
def admin():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('index'))
    return render_template('admin.html')

# API Routes
@app.route('/api/sip/register', methods=['POST'])
@login_required
def sip_register():
    data = request.get_json()
    session_id = str(uuid.uuid4())
    
    # Deactivate previous registrations
    SipRegistration.query.filter_by(user_id=current_user.id, is_active=True).update({'is_active': False})
    
    # Create new registration
    registration = SipRegistration(
        user_id=current_user.id,
        session_id=session_id,
        expires_at=datetime.utcnow() + timedelta(hours=1),
        user_agent=request.headers.get('User-Agent'),
        ip_address=request.remote_addr
    )
    
    db.session.add(registration)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'session_id': session_id,
        'sip_config': {
            'username': current_user.sip_username,
            'password': current_user.sip_password,
            'domain': current_user.sip_domain or app.config['SIP_DOMAIN'],
            'server': app.config['SIP_SERVER'],
            'port': app.config['SIP_PORT']
        }
    })

@app.route('/api/sip/unregister', methods=['POST'])
@login_required
def sip_unregister():
    SipRegistration.query.filter_by(user_id=current_user.id, is_active=True).update({'is_active': False})
    db.session.commit()
    return jsonify({'success': True})

@app.route('/api/calls', methods=['GET'])
@login_required
def get_calls():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    calls = CallLog.query.filter(
        (CallLog.caller_id == current_user.id) | (CallLog.callee_id == current_user.id)
    ).order_by(CallLog.call_start.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'calls': [call.to_dict() for call in calls.items],
        'total': calls.total,
        'pages': calls.pages,
        'current_page': page
    })

@app.route('/api/calls', methods=['POST'])
@login_required
def log_call():
    data = request.get_json()

    # Find callee by number
    callee = User.query.filter_by(sip_username=data.get('callee_number')).first()

    call_log = CallLog(
        caller_id=current_user.id,
        callee_id=callee.id if callee else None,
        caller_number=current_user.sip_username,
        callee_number=data.get('callee_number'),
        call_start=datetime.utcnow(),
        status='initiated',
        call_type='outbound'
    )

    db.session.add(call_log)
    db.session.commit()

    # Emit real-time update
    socketio.emit('call_update', call_log.to_dict(), room='admin')

    return jsonify({'success': True, 'call_id': call_log.id})

# Admin API Routes
@app.route('/api/admin/calls', methods=['GET'])
@login_required
def admin_get_calls():
    if not current_user.is_admin:
        return jsonify({'error': 'Access denied'}), 403

    limit = request.args.get('limit', 50, type=int)
    calls = CallLog.query.order_by(CallLog.call_start.desc()).limit(limit).all()

    return jsonify({
        'calls': [call.to_dict() for call in calls]
    })

@app.route('/api/admin/online-users', methods=['GET'])
@login_required
def admin_get_online_users():
    if not current_user.is_admin:
        return jsonify({'error': 'Access denied'}), 403

    # Get users with active SIP registrations
    active_registrations = SipRegistration.query.filter_by(is_active=True).all()
    user_ids = [reg.user_id for reg in active_registrations]
    users = User.query.filter(User.id.in_(user_ids)).all()

    return jsonify({
        'users': [user.to_dict() for user in users]
    })

@app.route('/api/admin/statistics', methods=['GET'])
@login_required
def admin_get_statistics():
    if not current_user.is_admin:
        return jsonify({'error': 'Access denied'}), 403

    today = datetime.utcnow().date()

    # Total calls today
    total_calls_today = CallLog.query.filter(
        CallLog.call_start >= today
    ).count()

    # Average call duration
    completed_calls = CallLog.query.filter(
        CallLog.status == 'completed',
        CallLog.duration.isnot(None)
    ).all()

    avg_duration = 0
    if completed_calls:
        total_duration = sum(call.duration for call in completed_calls)
        avg_duration = total_duration // len(completed_calls)

    return jsonify({
        'total_calls_today': total_calls_today,
        'avg_duration': avg_duration
    })

@app.route('/api/admin/users', methods=['POST'])
@login_required
def admin_add_user():
    if not current_user.is_admin:
        return jsonify({'error': 'Access denied'}), 403

    data = request.get_json()

    # Check if username or sip_username already exists
    if User.query.filter_by(username=data.get('username')).first():
        return jsonify({'success': False, 'message': 'Username already exists'})

    if User.query.filter_by(sip_username=data.get('sip_username')).first():
        return jsonify({'success': False, 'message': 'SIP username already exists'})

    user = User(
        username=data.get('username'),
        email=data.get('email'),
        sip_username=data.get('sip_username'),
        sip_password=data.get('password'),  # In production, generate a secure SIP password
        sip_domain=app.config['SIP_DOMAIN']
    )
    user.set_password(data.get('password'))

    db.session.add(user)
    db.session.commit()

    return jsonify({'success': True, 'user_id': user.id})

# Socket.IO Events
@socketio.on('connect')
def handle_connect():
    if current_user.is_authenticated:
        join_room(f'user_{current_user.id}')
        if current_user.is_admin:
            join_room('admin')
        emit('connected', {'message': 'Connected to softphone server'})

@socketio.on('disconnect')
def handle_disconnect():
    if current_user.is_authenticated:
        leave_room(f'user_{current_user.id}')
        if current_user.is_admin:
            leave_room('admin')

@socketio.on('call_status')
def handle_call_status(data):
    if current_user.is_authenticated:
        call_id = data.get('call_id')
        status = data.get('status')
        
        call_log = CallLog.query.get(call_id)
        if call_log:
            call_log.status = status
            if status in ['completed', 'failed', 'busy']:
                call_log.call_end = datetime.utcnow()
                if call_log.call_start:
                    call_log.duration = int((call_log.call_end - call_log.call_start).total_seconds())
            
            db.session.commit()
            
            # Emit update to admin
            socketio.emit('call_update', call_log.to_dict(), room='admin')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Create admin user if not exists
        admin = User.query.filter_by(username=app.config['ADMIN_USERNAME']).first()
        if not admin:
            admin = User(
                username=app.config['ADMIN_USERNAME'],
                email='<EMAIL>',
                sip_username='admin',
                sip_password='admin123',
                sip_domain=app.config['SIP_DOMAIN'],
                is_admin=True
            )
            admin.set_password(app.config['ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()

    # Run with threading mode for compatibility
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
