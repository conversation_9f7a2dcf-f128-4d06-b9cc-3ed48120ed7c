#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add new users to the Softphone system.
Usage: python add_user.py
"""

from app import app, db
from models import User

def add_user():
    """Interactive script to add a new user."""
    
    print("=== Add New User to Softphone System ===\n")
    
    # Get user input
    username = input("Enter username: ").strip()
    email = input("Enter email: ").strip()
    phone_number = input("Enter phone number (optional, e.g., +966501234567): ").strip()
    sip_username = input("Enter SIP username (extension): ").strip()
    password = input("Enter password: ").strip()
    is_admin = input("Is admin user? (y/N): ").strip().lower() == 'y'
    
    with app.app_context():
        # Check if username already exists
        if User.query.filter_by(username=username).first():
            print(f"Error: Username '{username}' already exists!")
            return False

        # Check if SIP username already exists
        if User.query.filter_by(sip_username=sip_username).first():
            print(f"Error: SIP username '{sip_username}' already exists!")
            return False

        # Check if phone number already exists (if provided)
        if phone_number and User.query.filter_by(phone_number=phone_number).first():
            print(f"Error: Phone number '{phone_number}' already exists!")
            return False
        
        # Create new user
        user = User(
            username=username,
            email=email,
            phone_number=phone_number if phone_number else None,
            sip_username=sip_username,
            sip_password=password,  # In production, generate a secure SIP password
            sip_domain=app.config['SIP_DOMAIN'],
            is_admin=is_admin
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        print(f"\nUser '{username}' created successfully!")
        print(f"- Username: {username}")
        print(f"- Email: {email}")
        print(f"- Phone: {phone_number if phone_number else 'Not provided'}")
        print(f"- SIP Extension: {sip_username}")
        print(f"- Admin: {'Yes' if is_admin else 'No'}")
        print(f"- Login URL: http://localhost:5099")
        
        return True

def list_users():
    """List all users in the system."""
    
    print("=== Current Users in System ===\n")
    
    with app.app_context():
        users = User.query.all()
        
        if not users:
            print("No users found.")
            return
        
        print(f"{'Username':<15} {'Email':<25} {'Phone':<15} {'SIP Ext':<10} {'Admin':<8} {'Active':<8}")
        print("-" * 90)

        for user in users:
            admin_status = "Yes" if user.is_admin else "No"
            active_status = "Yes" if user.is_active else "No"
            phone_display = user.phone_number if user.phone_number else "N/A"
            print(f"{user.username:<15} {user.email:<25} {phone_display:<15} {user.sip_username:<10} {admin_status:<8} {active_status:<8}")

def main():
    """Main function."""
    
    while True:
        print("\n=== Softphone User Management ===")
        print("1. Add new user")
        print("2. List all users")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == '1':
            add_user()
        elif choice == '2':
            list_users()
        elif choice == '3':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")

if __name__ == '__main__':
    main()
