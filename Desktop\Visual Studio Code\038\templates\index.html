<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Softphone - {{ user.username }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-left">
                <h1><i class="fas fa-phone"></i> Softphone</h1>
                <span class="user-info">Welcome, {{ user.username }}</span>
            </div>
            <div class="header-right">
                <span id="connection-status" class="status disconnected">
                    <i class="fas fa-circle"></i> Disconnected
                </span>
                {% if user.is_admin %}
                <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                    <i class="fas fa-cog"></i> Admin
                </a>
                {% endif %}
                <a href="{{ url_for('logout') }}" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </header>

        <main class="main-content">
            <div class="phone-panel">
                <div class="display">
                    <input type="text" id="phone-number" placeholder="Enter phone number" class="number-display">
                    <div class="call-info" id="call-info" style="display: none;">
                        <div class="call-status" id="call-status">Connecting...</div>
                        <div class="call-duration" id="call-duration">00:00</div>
                    </div>
                </div>

                <div class="keypad">
                    <div class="keypad-row">
                        <button class="key" data-digit="1">1</button>
                        <button class="key" data-digit="2">2<span>ABC</span></button>
                        <button class="key" data-digit="3">3<span>DEF</span></button>
                    </div>
                    <div class="keypad-row">
                        <button class="key" data-digit="4">4<span>GHI</span></button>
                        <button class="key" data-digit="5">5<span>JKL</span></button>
                        <button class="key" data-digit="6">6<span>MNO</span></button>
                    </div>
                    <div class="keypad-row">
                        <button class="key" data-digit="7">7<span>PQRS</span></button>
                        <button class="key" data-digit="8">8<span>TUV</span></button>
                        <button class="key" data-digit="9">9<span>WXYZ</span></button>
                    </div>
                    <div class="keypad-row">
                        <button class="key" data-digit="*">*</button>
                        <button class="key" data-digit="0">0<span>+</span></button>
                        <button class="key" data-digit="#">#</button>
                    </div>
                </div>

                <div class="call-controls">
                    <button id="call-btn" class="btn btn-call" disabled>
                        <i class="fas fa-phone"></i> Call
                    </button>
                    <button id="hangup-btn" class="btn btn-hangup" style="display: none;">
                        <i class="fas fa-phone-slash"></i> Hang Up
                    </button>
                    <button id="clear-btn" class="btn btn-secondary">
                        <i class="fas fa-backspace"></i> Clear
                    </button>
                </div>

                <div class="volume-controls">
                    <label for="mic-volume">Microphone:</label>
                    <input type="range" id="mic-volume" min="0" max="100" value="80">
                    <label for="speaker-volume">Speaker:</label>
                    <input type="range" id="speaker-volume" min="0" max="100" value="80">
                </div>
            </div>

            <div class="call-history">
                <h3><i class="fas fa-history"></i> Call History</h3>
                <div class="history-controls">
                    <button id="refresh-history" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div id="call-list" class="call-list">
                    <div class="loading">Loading call history...</div>
                </div>
                <div class="pagination" id="pagination" style="display: none;">
                    <button id="prev-page" class="btn btn-secondary">Previous</button>
                    <span id="page-info">Page 1 of 1</span>
                    <button id="next-page" class="btn btn-secondary">Next</button>
                </div>
            </div>
        </main>
    </div>

    <!-- Audio elements -->
    <audio id="remote-audio" autoplay></audio>
    <audio id="ringtone" loop>
        <source src="{{ url_for('static', filename='audio/ringtone.mp3') }}" type="audio/mpeg">
    </audio>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jssip@3.10.1/dist/jssip.min.js"></script>
    <script>
        // Configuration from Flask
        window.CONFIG = {
            SIP_SERVER: "{{ config.SIP_SERVER }}",
            SIP_PORT: {{ config.SIP_PORT }},
            SIP_DOMAIN: "{{ config.SIP_DOMAIN }}",
            STUN_SERVERS: {{ config.STUN_SERVERS | tojsonfilter }},
            USER: {
                id: {{ user.id }},
                username: "{{ user.username }}",
                sip_username: "{{ user.sip_username }}"
            }
        };
    </script>
    <script src="{{ url_for('static', filename='js/softphone.js') }}"></script>
</body>
</html>
