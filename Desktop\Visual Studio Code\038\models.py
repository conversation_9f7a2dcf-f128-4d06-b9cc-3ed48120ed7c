from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
import bcrypt

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    sip_username = db.Column(db.String(80), unique=True, nullable=False)
    sip_password = db.Column(db.String(128))
    sip_domain = db.Column(db.String(120))
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationship with call logs
    calls_made = db.relationship('CallLog', foreign_keys='CallLog.caller_id', backref='caller')
    calls_received = db.relationship('CallLog', foreign_keys='CallLog.callee_id', backref='callee')
    
    def set_password(self, password):
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def check_password(self, password):
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'sip_username': self.sip_username,
            'sip_domain': self.sip_domain,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class CallLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    caller_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    callee_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    caller_number = db.Column(db.String(50), nullable=False)
    callee_number = db.Column(db.String(50), nullable=False)
    call_start = db.Column(db.DateTime, default=datetime.utcnow)
    call_end = db.Column(db.DateTime)
    duration = db.Column(db.Integer)  # Duration in seconds
    status = db.Column(db.String(20))  # 'completed', 'missed', 'busy', 'failed'
    call_type = db.Column(db.String(20))  # 'inbound', 'outbound'
    
    def to_dict(self):
        return {
            'id': self.id,
            'caller_number': self.caller_number,
            'callee_number': self.callee_number,
            'call_start': self.call_start.isoformat() if self.call_start else None,
            'call_end': self.call_end.isoformat() if self.call_end else None,
            'duration': self.duration,
            'status': self.status,
            'call_type': self.call_type,
            'caller_name': self.caller.username if self.caller else 'Unknown',
            'callee_name': self.callee.username if self.callee else 'Unknown'
        }

class SipRegistration(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    session_id = db.Column(db.String(128), unique=True, nullable=False)
    registered_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime)
    user_agent = db.Column(db.String(255))
    ip_address = db.Column(db.String(45))
    is_active = db.Column(db.Boolean, default=True)
    
    user = db.relationship('User', backref='sip_registrations')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'registered_at': self.registered_at.isoformat() if self.registered_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'is_active': self.is_active,
            'username': self.user.username if self.user else 'Unknown'
        }
