import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-this'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///softphone.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # SIP Server Configuration
    SIP_SERVER = os.environ.get('SIP_SERVER') or 'your-asterisk-server.com'
    SIP_PORT = int(os.environ.get('SIP_PORT') or 5060)
    SIP_DOMAIN = os.environ.get('SIP_DOMAIN') or 'your-domain.com'
    
    # WebRTC Configuration
    STUN_SERVERS = [
        'stun:stun.l.google.com:19302',
        'stun:stun1.l.google.com:19302'
    ]
    
    # Admin Configuration
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
